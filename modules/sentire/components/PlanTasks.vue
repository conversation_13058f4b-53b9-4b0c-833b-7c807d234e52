<script setup lang="ts">
import Markdown from "~/components/Markdown.vue";
import CounterFlip from "./CounterFlip.vue";
import { useCurrentThreadStore } from "../stores";
import {
  PlusIcon,
  CheckCircleIcon,
  MinusIcon,
  LoaderCircleIcon,
  ChevronsUpDownIcon,
  CircleDashedIcon,
  CircleStopIcon,
} from "lucide-vue-next";

const emit = defineEmits<{
  (e: "expand-changed"): void;
}>();

const currentThreadStore = useCurrentThreadStore();
const { plans, status, summary, isRunning } = storeToRefs(currentThreadStore);
const isExpanded = ref(false);
const toggleExpand = async () => {
  isExpanded.value = !isExpanded.value;
  await nextTick();
  emit("expand-changed");
};
const initialPlansLength = ref(0);
const allPlans = computed(() => {
  if (summary.value) {
    return plans.value.map((plan) => ({ ...plan, status: "completed" }));
  }
  return plans.value;
});

watch(
  () => allPlans.value.length,
  (newLen, oldLen) => {
    if (
      oldLen === 0 &&
      newLen > 0 &&
      initialPlansLength.value === 0 &&
      status.value !== "pending"
    ) {
      initialPlansLength.value = newLen;
    }
  }
);
const totalCount = computed(() => {
  return allPlans.value.length;
});
const doneCount = computed(() => {
  return allPlans.value.filter((x) => x.status === "completed").length;
});
const currentPlanIndex = computed(() => {
  //return allPlans.value.filter((x) => x.status === "completed").length;
  const index = allPlans.value.findIndex(
    (p) => p.status === "running" || p.status === "pending"
  );
  if (index === -1) {
    return allPlans.value.length;
  } else {
    return index + 1;
  }
});
const runningTask = computed(() => {
  const task = allPlans.value.find(
    (x) => x.status === "running" || x.status === "pending"
  );
  if (task) {
    return task;
  } else {
    return allPlans.value[allPlans.value.length - 1];
  }
});
const isAllTaskDone = computed(() => {
  return totalCount.value === doneCount.value && totalCount.value > 0;
});
watch(status, (newStatus) => {
  if (newStatus === "pending") {
    isExpanded.value = false;
  }
});
</script>

<template>
  <div
    v-if="allPlans.length > 0"
    class="glass-container mb-3"
  >
    <div
      class="px-4 py-3 flex justify-between items-center"
      :class="{
        'border-b border-white/30': isExpanded,
      }"
    >
      <div class="flex items-center gap-2 flex-1">
        <div v-if="isExpanded" class="text-gray-800 font-medium">Process Tasks</div>
        <div v-else class="flex items-center gap-2">
          <CheckCircleIcon
            v-if="isAllTaskDone"
            :size="18"
            class="text-green-600"
          />
          <CircleStopIcon
            v-else-if="!isRunning"
            :size="18"
            class="text-gray-500"
          />
          <LoaderCircleIcon v-else :size="18" class="animate-spin text-gray-600" />

          <div class="truncate flex-1 w-[650px] text-gray-800">
            <Markdown :source="runningTask?.title ?? ''" class="prose-sm" />
          </div>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <div
          class="text-sm text-gray-600 w-10 text-right flex items-center justify-end gap-0.5"
        >
          <CounterFlip :value="currentPlanIndex" />
          <span>/</span>
          <CounterFlip :value="totalCount" />
        </div>
        <ChevronsUpDownIcon
          :size="16"
          class="cursor-pointer text-gray-600 hover:text-gray-800 transition-colors"
          @click="toggleExpand"
        />
      </div>
    </div>
    <div
      class="transition-all duration-300 ease-in-out"
      :class="{
        'h-0 overflow-hidden': !isExpanded,
        'px-4 py-2': isExpanded,
      }"
    >
      <div
        v-for="(plan, idx) in allPlans"
        :key="plan.id"
        class="flex items-center gap-2 py-1"
      >
        <CheckCircleIcon
          v-if="plan.status === 'completed' && !plan.isRemoved"
          :size="16"
          class="text-green-600 size-4 min-w-4"
        />
        <LoaderCircleIcon
          v-else-if="plan.status === 'running' && isRunning"
          :size="16"
          class="text-gray-600 animate-spin min-w-4 duration-500"
        />
        <CircleDashedIcon
          v-else-if="plan.status === 'pending' && isRunning"
          :size="16"
          class="text-gray-500 animate-spin min-w-4 duration-500"
        />
        <CircleStopIcon
          v-else-if="!isRunning"
          :size="16"
          class="text-gray-500 min-w-4 duration-500"
        />

        <div class="flex items-center gap-2">
          <template v-if="idx >= initialPlansLength">
            <MinusIcon
              v-if="plan.isRemoved"
              :size="16"
              class="text-red-600 min-w-4"
            />
            <PlusIcon
              v-else-if="plan.isNewAdded"
              :size="16"
              class="text-blue-600 min-w-4"
            />
          </template>
        </div>
        <div
          class="text-gray-800"
          :class="{
            'line-through opacity-60': plan.isRemoved ?? false,
          }"
        >
          <Markdown :source="plan.title" class="prose-sm" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 磨玻璃效果容器 */
.glass-container {
  /* 半透明白色背景 - 与ScrollToBottom保持一致 */
  background: rgba(255, 255, 255, 0.25);

  /* 磨玻璃模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* 边框增强玻璃质感 */
  border: 1px solid rgba(255, 255, 255, 0.3);

  /* 圆角 */
  border-radius: var(--radius-md-new);

  /* 渐变背景增加深度 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );

  /* 柔和的浮动立体效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.glass-container:hover {
  background: rgba(255, 255, 255, 0.35);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  /* 悬停时增强阴影效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}
</style>
